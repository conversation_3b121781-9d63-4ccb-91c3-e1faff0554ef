{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/sections/Hero.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useTranslations } from \"next-intl\";\n\nexport default function Hero() {\n  const tHero = useTranslations(\"Hero\");\n  const tCommon = useTranslations(\"Common\");\n  const tPersonas = useTranslations(\"Personas\");\n  const tContent = useTranslations(\"ContentGeneration\");\n  return (\n    <section className=\"hero\">\n      <div className=\"hero-content\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n        >\n          <div className=\"hero-badge\">{tHero(\"badge\")}</div>\n          <h1 className=\"hero-title !text-5xl\">\n            {tHero(\"title\")}\n            <br />\n            <span className=\"gradient-text mt-4 inline-block\">\n              {tHero(\"subtitle\")}\n            </span>\n          </h1>\n          <p className=\"hero-subtitle mt-4\">{tHero(\"description\")}</p>\n          <div className=\"hero-actions\">\n            <button className=\"btn-primary btn-large\">\n              {tCommon(\"getEarlyAccess\")}\n            </button>\n            <button className=\"btn-secondary btn-large\">\n              {tCommon(\"watchDemo\")}\n            </button>\n          </div>\n          <div className=\"hero-stats\">\n            <div className=\"stat\">\n              <span className=\"stat-number\">{tHero(\"stats.ai\")}</span>\n              <span className=\"stat-label\">{tHero(\"stats.aiLabel\")}</span>\n            </div>\n            <div className=\"stat\">\n              <span className=\"stat-number\">{tHero(\"stats.live\")}</span>\n              <span className=\"stat-label\">{tHero(\"stats.liveLabel\")}</span>\n            </div>\n            <div className=\"stat\">\n              <span className=\"stat-number\">{tHero(\"stats.infinite\")}</span>\n              <span className=\"stat-label\">{tHero(\"stats.infiniteLabel\")}</span>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n\n      <div className=\"hero-visual\">\n        <div className=\"hero-mockup\">\n          <motion.div\n            className=\"floating-card card-1\"\n            animate={{\n              y: [0, -20, 0],\n              rotate: [0, 2, 0],\n            }}\n            transition={{\n              duration: 6,\n              repeat: Infinity,\n              ease: \"easeInOut\",\n            }}\n          >\n            <div className=\"persona-preview\">\n              <img\n                src=\"data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 60 60' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Ccircle cx='30' cy='30' r='30' fill='url(%23gradient1)'/%3E%3Cdefs%3E%3ClinearGradient id='gradient1' x1='0' y1='0' x2='60' y2='60'%3E%3Cstop stop-color='%23ff2e4d'/%3E%3Cstop offset='1' stop-color='%237c3aed'/%3E%3C/linearGradient%3E%3C/defs%3E%3C/svg%3E\"\n                alt={tPersonas(\"fashionQianqian.name\")}\n              />\n              <div>\n                <h4>{tPersonas(\"fashionQianqian.name\")}</h4>\n                <p>{tPersonas(\"fashionQianqian.type\")}</p>\n              </div>\n            </div>\n          </motion.div>\n\n          <motion.div\n            className=\"floating-card card-2\"\n            animate={{\n              y: [0, -15, 0],\n              rotate: [0, -1, 0],\n            }}\n            transition={{\n              duration: 8,\n              repeat: Infinity,\n              ease: \"easeInOut\",\n              delay: 2,\n            }}\n          >\n            <div className=\"platform-icons\">\n              <div className=\"source-icon !bg-bg-primary\">\n                <svg\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  strokeWidth=\"2\"\n                >\n                  <path d=\"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z\" />\n                  <polyline points=\"22,6 12,13 2,6\" />\n                </svg>\n              </div>\n              <div className=\"source-icon !bg-bg-primary\">\n                <svg\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  strokeWidth=\"2\"\n                >\n                  <path d=\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\" />\n                  <polyline points=\"14,2 14,8 20,8\" />\n                  <line x1=\"16\" y1=\"13\" x2=\"8\" y2=\"13\" />\n                  <line x1=\"16\" y1=\"17\" x2=\"8\" y2=\"17\" />\n                  <polyline points=\"10,9 9,9 8,9\" />\n                </svg>\n              </div>\n              <div className=\"source-icon !bg-bg-primary\">\n                <svg\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  strokeWidth=\"2\"\n                >\n                  <path d=\"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71\" />\n                  <path d=\"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71\" />\n                </svg>\n              </div>\n              <div className=\"source-icon !bg-bg-primary\">\n                <svg\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  strokeWidth=\"2\"\n                >\n                  <rect x=\"3\" y=\"4\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\" />\n                  <line x1=\"16\" y1=\"2\" x2=\"16\" y2=\"6\" />\n                  <line x1=\"8\" y1=\"2\" x2=\"8\" y2=\"6\" />\n                  <line x1=\"3\" y1=\"10\" x2=\"21\" y2=\"10\" />\n                  <path d=\"M8 14h.01\" />\n                  <path d=\"M12 14h.01\" />\n                  <path d=\"M16 14h.01\" />\n                  <path d=\"M8 18h.01\" />\n                  <path d=\"M12 18h.01\" />\n                </svg>\n              </div>\n            </div>\n          </motion.div>\n\n          <motion.div\n            className=\"floating-card card-3\"\n            animate={{\n              y: [0, -25, 0],\n              rotate: [0, 1, 0],\n            }}\n            transition={{\n              duration: 7,\n              repeat: Infinity,\n              ease: \"easeInOut\",\n              delay: 4,\n            }}\n          >\n            <div className=\"content-preview\">\n              <div className=\"preview-image\">\n                <svg width=\"80\" height=\"60\" viewBox=\"0 0 80 60\" fill=\"none\">\n                  <defs>\n                    <linearGradient\n                      id=\"autumnBg\"\n                      x1=\"0%\"\n                      y1=\"0%\"\n                      x2=\"100%\"\n                      y2=\"100%\"\n                    >\n                      <stop offset=\"0%\" style={{ stopColor: \"#FFA07A\" }} />\n                      <stop offset=\"50%\" style={{ stopColor: \"#CD853F\" }} />\n                      <stop offset=\"100%\" style={{ stopColor: \"#8B4513\" }} />\n                    </linearGradient>\n                    <linearGradient\n                      id=\"sweaterGrad\"\n                      x1=\"0%\"\n                      y1=\"0%\"\n                      x2=\"100%\"\n                      y2=\"100%\"\n                    >\n                      <stop offset=\"0%\" style={{ stopColor: \"#D2691E\" }} />\n                      <stop offset=\"100%\" style={{ stopColor: \"#A0522D\" }} />\n                    </linearGradient>\n                  </defs>\n                  <rect width=\"80\" height=\"60\" fill=\"url(#autumnBg)\" rx=\"6\" />\n\n                  <g opacity=\"0.3\">\n                    <path d=\"M15 12 Q18 8 21 12 Q18 16 15 12\" fill=\"#8B0000\" />\n                    <path d=\"M62 15 Q65 11 68 15 Q65 19 62 15\" fill=\"#FF6347\" />\n                    <path d=\"M8 35 Q11 31 14 35 Q11 39 8 35\" fill=\"#CD853F\" />\n                    <path d=\"M70 45 Q73 41 76 45 Q73 49 70 45\" fill=\"#B22222\" />\n                    <path d=\"M25 50 Q28 46 31 50 Q28 54 25 50\" fill=\"#FF8C00\" />\n                  </g>\n\n                  <ellipse\n                    cx=\"40\"\n                    cy=\"35\"\n                    rx=\"18\"\n                    ry=\"20\"\n                    fill=\"url(#sweaterGrad)\"\n                  />\n                  <ellipse cx=\"40\" cy=\"32\" rx=\"15\" ry=\"16\" fill=\"#DEB887\" />\n\n                  <line\n                    x1=\"30\"\n                    y1=\"25\"\n                    x2=\"50\"\n                    y2=\"25\"\n                    stroke=\"#CD853F\"\n                    strokeWidth=\"0.5\"\n                  />\n                  <line\n                    x1=\"30\"\n                    y1=\"30\"\n                    x2=\"50\"\n                    y2=\"30\"\n                    stroke=\"#CD853F\"\n                    strokeWidth=\"0.5\"\n                  />\n                  <line\n                    x1=\"30\"\n                    y1=\"35\"\n                    x2=\"50\"\n                    y2=\"35\"\n                    stroke=\"#CD853F\"\n                    strokeWidth=\"0.5\"\n                  />\n\n                  <ellipse cx=\"40\" cy=\"18\" rx=\"12\" ry=\"4\" fill=\"#8B0000\" />\n                  <rect x=\"48\" y=\"15\" width=\"3\" height=\"12\" fill=\"#8B0000\" />\n                  <rect x=\"52\" y=\"17\" width=\"2\" height=\"8\" fill=\"#CD853F\" />\n\n                  <rect\n                    x=\"32\"\n                    y=\"48\"\n                    width=\"16\"\n                    height=\"10\"\n                    fill=\"#4682B4\"\n                    rx=\"2\"\n                  />\n                  <line\n                    x1=\"34\"\n                    y1=\"50\"\n                    x2=\"34\"\n                    y2=\"56\"\n                    stroke=\"#191970\"\n                    strokeWidth=\"0.5\"\n                  />\n                  <line\n                    x1=\"46\"\n                    y1=\"50\"\n                    x2=\"46\"\n                    y2=\"56\"\n                    stroke=\"#191970\"\n                    strokeWidth=\"0.5\"\n                  />\n\n                  <ellipse cx=\"36\" cy=\"57\" rx=\"4\" ry=\"2\" fill=\"#654321\" />\n                  <ellipse cx=\"44\" cy=\"57\" rx=\"4\" ry=\"2\" fill=\"#654321\" />\n\n                  <circle cx=\"20\" cy=\"20\" r=\"1\" fill=\"#FFD700\" opacity=\"0.8\" />\n                  <circle cx=\"60\" cy=\"25\" r=\"1\" fill=\"#FFD700\" opacity=\"0.8\" />\n                  <circle cx=\"15\" cy=\"45\" r=\"1\" fill=\"#FFD700\" opacity=\"0.6\" />\n                  <circle cx=\"65\" cy=\"50\" r=\"1\" fill=\"#FFD700\" opacity=\"0.6\" />\n                </svg>\n              </div>\n              <p>&quot;{tContent(\"previewContent\")}&quot;</p>\n              <span className=\"preview-meta\">{tContent(\"aiGenerated\")}</span>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,QAAQ,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC9B,MAAM,UAAU,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAChC,MAAM,YAAY,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAClC,MAAM,WAAW,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IACjC,qBACE,6LAAC;QAAQ,WAAU;;0BACjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,6LAAC;4BAAI,WAAU;sCAAc,MAAM;;;;;;sCACnC,6LAAC;4BAAG,WAAU;;gCACX,MAAM;8CACP,6LAAC;;;;;8CACD,6LAAC;oCAAK,WAAU;8CACb,MAAM;;;;;;;;;;;;sCAGX,6LAAC;4BAAE,WAAU;sCAAsB,MAAM;;;;;;sCACzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAO,WAAU;8CACf,QAAQ;;;;;;8CAEX,6LAAC;oCAAO,WAAU;8CACf,QAAQ;;;;;;;;;;;;sCAGb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAe,MAAM;;;;;;sDACrC,6LAAC;4CAAK,WAAU;sDAAc,MAAM;;;;;;;;;;;;8CAEtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAe,MAAM;;;;;;sDACrC,6LAAC;4CAAK,WAAU;sDAAc,MAAM;;;;;;;;;;;;8CAEtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAe,MAAM;;;;;;sDACrC,6LAAC;4CAAK,WAAU;sDAAc,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM5C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCACP,GAAG;oCAAC;oCAAG,CAAC;oCAAI;iCAAE;gCACd,QAAQ;oCAAC;oCAAG;oCAAG;iCAAE;4BACnB;4BACA,YAAY;gCACV,UAAU;gCACV,QAAQ;gCACR,MAAM;4BACR;sCAEA,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,KAAI;wCACJ,KAAK,UAAU;;;;;;kDAEjB,6LAAC;;0DACC,6LAAC;0DAAI,UAAU;;;;;;0DACf,6LAAC;0DAAG,UAAU;;;;;;;;;;;;;;;;;;;;;;;sCAKpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCACP,GAAG;oCAAC;oCAAG,CAAC;oCAAI;iCAAE;gCACd,QAAQ;oCAAC;oCAAG,CAAC;oCAAG;iCAAE;4BACpB;4BACA,YAAY;gCACV,UAAU;gCACV,QAAQ;gCACR,MAAM;gCACN,OAAO;4BACT;sCAEA,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,OAAM;4CACN,QAAO;4CACP,SAAQ;4CACR,MAAK;4CACL,QAAO;4CACP,aAAY;;8DAEZ,6LAAC;oDAAK,GAAE;;;;;;8DACR,6LAAC;oDAAS,QAAO;;;;;;;;;;;;;;;;;kDAGrB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,OAAM;4CACN,QAAO;4CACP,SAAQ;4CACR,MAAK;4CACL,QAAO;4CACP,aAAY;;8DAEZ,6LAAC;oDAAK,GAAE;;;;;;8DACR,6LAAC;oDAAS,QAAO;;;;;;8DACjB,6LAAC;oDAAK,IAAG;oDAAK,IAAG;oDAAK,IAAG;oDAAI,IAAG;;;;;;8DAChC,6LAAC;oDAAK,IAAG;oDAAK,IAAG;oDAAK,IAAG;oDAAI,IAAG;;;;;;8DAChC,6LAAC;oDAAS,QAAO;;;;;;;;;;;;;;;;;kDAGrB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,OAAM;4CACN,QAAO;4CACP,SAAQ;4CACR,MAAK;4CACL,QAAO;4CACP,aAAY;;8DAEZ,6LAAC;oDAAK,GAAE;;;;;;8DACR,6LAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;kDAGZ,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,OAAM;4CACN,QAAO;4CACP,SAAQ;4CACR,MAAK;4CACL,QAAO;4CACP,aAAY;;8DAEZ,6LAAC;oDAAK,GAAE;oDAAI,GAAE;oDAAI,OAAM;oDAAK,QAAO;oDAAK,IAAG;oDAAI,IAAG;;;;;;8DACnD,6LAAC;oDAAK,IAAG;oDAAK,IAAG;oDAAI,IAAG;oDAAK,IAAG;;;;;;8DAChC,6LAAC;oDAAK,IAAG;oDAAI,IAAG;oDAAI,IAAG;oDAAI,IAAG;;;;;;8DAC9B,6LAAC;oDAAK,IAAG;oDAAI,IAAG;oDAAK,IAAG;oDAAK,IAAG;;;;;;8DAChC,6LAAC;oDAAK,GAAE;;;;;;8DACR,6LAAC;oDAAK,GAAE;;;;;;8DACR,6LAAC;oDAAK,GAAE;;;;;;8DACR,6LAAC;oDAAK,GAAE;;;;;;8DACR,6LAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMhB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCACP,GAAG;oCAAC;oCAAG,CAAC;oCAAI;iCAAE;gCACd,QAAQ;oCAAC;oCAAG;oCAAG;iCAAE;4BACnB;4BACA,YAAY;gCACV,UAAU;gCACV,QAAQ;gCACR,MAAM;gCACN,OAAO;4BACT;sCAEA,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,OAAM;4CAAK,QAAO;4CAAK,SAAQ;4CAAY,MAAK;;8DACnD,6LAAC;;sEACC,6LAAC;4DACC,IAAG;4DACH,IAAG;4DACH,IAAG;4DACH,IAAG;4DACH,IAAG;;8EAEH,6LAAC;oEAAK,QAAO;oEAAK,OAAO;wEAAE,WAAW;oEAAU;;;;;;8EAChD,6LAAC;oEAAK,QAAO;oEAAM,OAAO;wEAAE,WAAW;oEAAU;;;;;;8EACjD,6LAAC;oEAAK,QAAO;oEAAO,OAAO;wEAAE,WAAW;oEAAU;;;;;;;;;;;;sEAEpD,6LAAC;4DACC,IAAG;4DACH,IAAG;4DACH,IAAG;4DACH,IAAG;4DACH,IAAG;;8EAEH,6LAAC;oEAAK,QAAO;oEAAK,OAAO;wEAAE,WAAW;oEAAU;;;;;;8EAChD,6LAAC;oEAAK,QAAO;oEAAO,OAAO;wEAAE,WAAW;oEAAU;;;;;;;;;;;;;;;;;;8DAGtD,6LAAC;oDAAK,OAAM;oDAAK,QAAO;oDAAK,MAAK;oDAAiB,IAAG;;;;;;8DAEtD,6LAAC;oDAAE,SAAQ;;sEACT,6LAAC;4DAAK,GAAE;4DAAkC,MAAK;;;;;;sEAC/C,6LAAC;4DAAK,GAAE;4DAAmC,MAAK;;;;;;sEAChD,6LAAC;4DAAK,GAAE;4DAAiC,MAAK;;;;;;sEAC9C,6LAAC;4DAAK,GAAE;4DAAmC,MAAK;;;;;;sEAChD,6LAAC;4DAAK,GAAE;4DAAmC,MAAK;;;;;;;;;;;;8DAGlD,6LAAC;oDACC,IAAG;oDACH,IAAG;oDACH,IAAG;oDACH,IAAG;oDACH,MAAK;;;;;;8DAEP,6LAAC;oDAAQ,IAAG;oDAAK,IAAG;oDAAK,IAAG;oDAAK,IAAG;oDAAK,MAAK;;;;;;8DAE9C,6LAAC;oDACC,IAAG;oDACH,IAAG;oDACH,IAAG;oDACH,IAAG;oDACH,QAAO;oDACP,aAAY;;;;;;8DAEd,6LAAC;oDACC,IAAG;oDACH,IAAG;oDACH,IAAG;oDACH,IAAG;oDACH,QAAO;oDACP,aAAY;;;;;;8DAEd,6LAAC;oDACC,IAAG;oDACH,IAAG;oDACH,IAAG;oDACH,IAAG;oDACH,QAAO;oDACP,aAAY;;;;;;8DAGd,6LAAC;oDAAQ,IAAG;oDAAK,IAAG;oDAAK,IAAG;oDAAK,IAAG;oDAAI,MAAK;;;;;;8DAC7C,6LAAC;oDAAK,GAAE;oDAAK,GAAE;oDAAK,OAAM;oDAAI,QAAO;oDAAK,MAAK;;;;;;8DAC/C,6LAAC;oDAAK,GAAE;oDAAK,GAAE;oDAAK,OAAM;oDAAI,QAAO;oDAAI,MAAK;;;;;;8DAE9C,6LAAC;oDACC,GAAE;oDACF,GAAE;oDACF,OAAM;oDACN,QAAO;oDACP,MAAK;oDACL,IAAG;;;;;;8DAEL,6LAAC;oDACC,IAAG;oDACH,IAAG;oDACH,IAAG;oDACH,IAAG;oDACH,QAAO;oDACP,aAAY;;;;;;8DAEd,6LAAC;oDACC,IAAG;oDACH,IAAG;oDACH,IAAG;oDACH,IAAG;oDACH,QAAO;oDACP,aAAY;;;;;;8DAGd,6LAAC;oDAAQ,IAAG;oDAAK,IAAG;oDAAK,IAAG;oDAAI,IAAG;oDAAI,MAAK;;;;;;8DAC5C,6LAAC;oDAAQ,IAAG;oDAAK,IAAG;oDAAK,IAAG;oDAAI,IAAG;oDAAI,MAAK;;;;;;8DAE5C,6LAAC;oDAAO,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAI,MAAK;oDAAU,SAAQ;;;;;;8DACrD,6LAAC;oDAAO,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAI,MAAK;oDAAU,SAAQ;;;;;;8DACrD,6LAAC;oDAAO,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAI,MAAK;oDAAU,SAAQ;;;;;;8DACrD,6LAAC;oDAAO,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAI,MAAK;oDAAU,SAAQ;;;;;;;;;;;;;;;;;kDAGzD,6LAAC;;4CAAE;4CAAO,SAAS;4CAAkB;;;;;;;kDACrC,6LAAC;wCAAK,WAAU;kDAAgB,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvD;GAzRwB;;QACR,yMAAA,CAAA,kBAAe;QACb,yMAAA,CAAA,kBAAe;QACb,yMAAA,CAAA,kBAAe;QAChB,yMAAA,CAAA,kBAAe;;;KAJV", "debugId": null}}, {"offset": {"line": 979, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/sections/FeaturesSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"framer-motion\";\nimport { useRef } from \"react\";\nimport { useTranslations } from \"next-intl\";\n\nexport default function FeaturesSection() {\n  const tFeatures = useTranslations(\"Features\");\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, margin: \"-100px\" });\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n      },\n    },\n  };\n\n  const cardVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6,\n        ease: [0.25, 0.1, 0.35, 1] as const,\n      },\n    },\n  };\n\n  return (\n    <section className=\"py-24 relative bg-bg-secondary w-full\" ref={ref}>\n      <div className=\"max-w-6xl mx-auto px-5\">\n        <div className=\"text-center mb-20\">\n          <h2 className=\"text-3xl lg:text-5xl font-bold text-white mb-6 leading-tight\">\n            {tFeatures(\"sectionTitle\", {\n              title: tFeatures(\"photoToPost\"),\n            })\n              .split(tFeatures(\"photoToPost\"))\n              .map((part, index) =>\n                index === 0 ? (\n                  part\n                ) : (\n                  <span key={index}>\n                    <span className=\"gradient-text\">\n                      {tFeatures(\"photoToPost\")}\n                    </span>\n                    {part}\n                  </span>\n                )\n              )}\n          </h2>\n          <p className=\"text-xl text-text-secondary max-w-3xl mx-auto leading-relaxed\">\n            {tFeatures(\"sectionSubtitle\")}\n          </p>\n        </div>\n\n        <motion.div\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto\"\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate={isInView ? \"visible\" : \"hidden\"}\n        >\n          {/* Camera Roll Feature - PRIMARY */}\n          <motion.div\n            className=\"bg-gradient-to-br from-red-500/10 to-purple-600/10 border border-red-500/20 rounded-2xl p-8 hover:border-red-500/40 transition-all duration-300\"\n            variants={cardVariants}\n          >\n            <div className=\"w-16 h-16 bg-gradient-to-br rounded-xl flex items-center justify-center mb-6\">\n              <div className=\"relative w-10 h-8\">\n                <div className=\"w-full h-full bg-white rounded-sm\"></div>\n                <div className=\"absolute top-1 left-1 w-3 h-3 bg-gray-800 rounded-full\"></div>\n                <div className=\"absolute top-0 right-0 w-1 h-1 bg-yellow-400 rounded-full\"></div>\n              </div>\n            </div>\n            <h3 className=\"text-xl font-bold text-white mb-4\">\n              {tFeatures(\"step1.title\")}\n            </h3>\n            <p className=\"text-text-secondary mb-6 leading-relaxed\">\n              {tFeatures(\"step1.description\")}\n            </p>\n            <ul className=\"space-y-3\">\n              <li className=\"flex items-center text-text-secondary text-sm\">\n                <span className=\"w-1.5 h-1.5 bg-red-500 rounded-full mr-3\"></span>\n                {tFeatures(\"step1.features.0\")}\n              </li>\n              <li className=\"flex items-center text-text-secondary text-sm\">\n                <span className=\"w-1.5 h-1.5 bg-red-500 rounded-full mr-3\"></span>\n                {tFeatures(\"step1.features.1\")}\n              </li>\n              <li className=\"flex items-center text-text-secondary text-sm\">\n                <span className=\"w-1.5 h-1.5 bg-red-500 rounded-full mr-3\"></span>\n                {tFeatures(\"step1.features.2\")}\n              </li>\n            </ul>\n          </motion.div>\n\n          {/* Persona Feature */}\n          <motion.div\n            className=\"bg-gradient-to-br from-purple-500/10 to-blue-600/10 border border-purple-500/20 rounded-2xl p-8 hover:border-purple-500/40 transition-all duration-300\"\n            variants={cardVariants}\n          >\n            <div className=\"w-16 h-16  relative rounded-xl flex items-center justify-center mb-6\">\n              <div className=\"flex gap-1 w-full\">\n                <div className=\"w-3 h-3 bg-accent-red rounded-full absolute top-4 left-6\"></div>\n                <div className=\"w-3 h-3 bg-accent-purple rounded-full absolute top-8 left-3\"></div>\n                <div className=\"w-3 h-3 bg-accent-cyan rounded-full absolute top-8 left-9\"></div>\n              </div>\n            </div>\n            <h3 className=\"text-xl font-bold text-white mb-4\">\n              {tFeatures(\"step2.title\")}\n            </h3>\n            <p className=\"text-text-secondary mb-6 leading-relaxed\">\n              {tFeatures(\"step2.description\")}\n            </p>\n            <ul className=\"space-y-3\">\n              <li className=\"flex items-center text-text-secondary text-sm\">\n                <span className=\"w-1.5 h-1.5 bg-purple-500 rounded-full mr-3\"></span>\n                {tFeatures(\"step2.features.0\")}\n              </li>\n              <li className=\"flex items-center text-text-secondary text-sm\">\n                <span className=\"w-1.5 h-1.5 bg-purple-500 rounded-full mr-3\"></span>\n                {tFeatures(\"step2.features.1\")}\n              </li>\n              <li className=\"flex items-center text-text-secondary text-sm\">\n                <span className=\"w-1.5 h-1.5 bg-purple-500 rounded-full mr-3\"></span>\n                {tFeatures(\"step2.features.2\")}\n              </li>\n            </ul>\n          </motion.div>\n\n          {/* Trend Detection */}\n          <motion.div\n            className=\"bg-gray-800/50 border border-gray-700 rounded-2xl p-8 hover:border-gray-600 transition-all duration-300\"\n            variants={cardVariants}\n          >\n            <div className=\"w-16 h-16 rounded-xl flex items-center justify-center mb-6\">\n              <div className=\"icon-trend\">\n                <div className=\"trend-line line-1\"></div>\n                <div className=\"trend-line line-2\"></div>\n                <div className=\"trend-line line-3\"></div>\n                <div className=\"trend-dot dot-1\"></div>\n                <div className=\"trend-dot dot-2\"></div>\n                <div className=\"trend-dot dot-3\"></div>\n              </div>\n            </div>\n            <h3 className=\"text-xl font-bold text-white mb-4\">\n              {tFeatures(\"step3.title\")}\n            </h3>\n            <p className=\"text-text-secondary mb-6 leading-relaxed\">\n              {tFeatures(\"step3.description\")}\n            </p>\n          </motion.div>\n\n          {/* AI Knowledge Integration */}\n          <motion.div\n            className=\"bg-gray-800/50 border border-gray-700 rounded-2xl p-8 hover:border-gray-600 transition-all duration-300\"\n            variants={cardVariants}\n          >\n            <div className=\"w-16 h-16  rounded-xl flex items-center justify-center mb-6\">\n              <div className=\"knowledge-sources\">\n                <div className=\"source-node doc\"></div>\n                <div className=\"source-node link\"></div>\n                <div className=\"source-node feed\"></div>\n                <div className=\"knowledge-center\"></div>\n                <div className=\"knowledge-flow flow-1\"></div>\n                <div className=\"knowledge-flow flow-2\"></div>\n                <div className=\"knowledge-flow flow-3\"></div>\n              </div>\n            </div>\n            <h3 className=\"text-xl font-bold text-white mb-4\">\n              {tFeatures(\"smartKnowledge.title\")}\n            </h3>\n            <p className=\"text-text-secondary mb-6 leading-relaxed\">\n              {tFeatures(\"smartKnowledge.description\")}\n            </p>\n          </motion.div>\n\n          {/* Trending Topics */}\n          <motion.div\n            className=\"bg-gray-800/50 border border-gray-700 rounded-2xl p-8 hover:border-gray-600 transition-all duration-300\"\n            variants={cardVariants}\n          >\n            <div className=\"w-16 h-16  rounded-xl flex items-center justify-center mb-6\">\n              <div className=\"feature-icon\">\n                <div className=\"icon-viral\">\n                  <div className=\"viral-bubble bubble-1\"></div>\n                  <div className=\"viral-bubble bubble-2\"></div>\n                  <div className=\"viral-bubble bubble-3\"></div>\n                  <div className=\"viral-pulse\"></div>\n                </div>\n              </div>\n            </div>\n            <h3 className=\"text-xl font-bold text-white mb-4\">\n              {tFeatures(\"alwaysOnTrend.title\")}\n            </h3>\n            <p className=\"text-text-secondary mb-6 leading-relaxed\">\n              {tFeatures(\"alwaysOnTrend.description\")}\n            </p>\n          </motion.div>\n\n          {/* Analytics */}\n          <motion.div\n            className=\"bg-gray-800/50 border border-gray-700 rounded-2xl p-8 hover:border-gray-600 transition-all duration-300\"\n            variants={cardVariants}\n          >\n            <div className=\"w-16 h-16 relative  rounded-xl flex items-center justify-center mb-6\">\n              <div className=\"icon-analytics\">\n                <div className=\"chart-bar bar-1\"></div>\n                <div className=\"chart-bar bar-2\"></div>\n                <div className=\"chart-bar bar-3\"></div>\n                <div className=\"chart-bar bar-4\"></div>\n                <div className=\"chart-line\"></div>\n              </div>\n            </div>\n            <h3 className=\"text-xl font-bold text-white mb-4\">\n              {tFeatures(\"performanceInsights.title\")}\n            </h3>\n            <p className=\"text-text-secondary mb-6 leading-relaxed\">\n              {tFeatures(\"performanceInsights.description\")}\n            </p>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,YAAY,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAClC,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAE/D,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;oBAAC;oBAAM;oBAAK;oBAAM;iBAAE;YAC5B;QACF;IACF;IAEA,qBACE,6LAAC;QAAQ,WAAU;QAAwC,KAAK;kBAC9D,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,UAAU,gBAAgB;gCACzB,OAAO,UAAU;4BACnB,GACG,KAAK,CAAC,UAAU,gBAChB,GAAG,CAAC,CAAC,MAAM,QACV,UAAU,IACR,qBAEA,6LAAC;;sDACC,6LAAC;4CAAK,WAAU;sDACb,UAAU;;;;;;wCAEZ;;mCAJQ;;;;;;;;;;sCASnB,6LAAC;4BAAE,WAAU;sCACV,UAAU;;;;;;;;;;;;8BAIf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU;oBACV,SAAQ;oBACR,SAAS,WAAW,YAAY;;sCAGhC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;;8CAEV,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;8CAGnB,6LAAC;oCAAG,WAAU;8CACX,UAAU;;;;;;8CAEb,6LAAC;oCAAE,WAAU;8CACV,UAAU;;;;;;8CAEb,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;;;;;;gDACf,UAAU;;;;;;;sDAEb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;;;;;;gDACf,UAAU;;;;;;;sDAEb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;;;;;;gDACf,UAAU;;;;;;;;;;;;;;;;;;;sCAMjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;;8CAEV,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;8CAGnB,6LAAC;oCAAG,WAAU;8CACX,UAAU;;;;;;8CAEb,6LAAC;oCAAE,WAAU;8CACV,UAAU;;;;;;8CAEb,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;;;;;;gDACf,UAAU;;;;;;;sDAEb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;;;;;;gDACf,UAAU;;;;;;;sDAEb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;;;;;;gDACf,UAAU;;;;;;;;;;;;;;;;;;;sCAMjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;;8CAEV,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;8CAGnB,6LAAC;oCAAG,WAAU;8CACX,UAAU;;;;;;8CAEb,6LAAC;oCAAE,WAAU;8CACV,UAAU;;;;;;;;;;;;sCAKf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;;8CAEV,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;8CAGnB,6LAAC;oCAAG,WAAU;8CACX,UAAU;;;;;;8CAEb,6LAAC;oCAAE,WAAU;8CACV,UAAU;;;;;;;;;;;;sCAKf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;;8CAEV,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAIrB,6LAAC;oCAAG,WAAU;8CACX,UAAU;;;;;;8CAEb,6LAAC;oCAAE,WAAU;8CACV,UAAU;;;;;;;;;;;;sCAKf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;;8CAEV,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;8CAGnB,6LAAC;oCAAG,WAAU;8CACX,UAAU;;;;;;8CAEb,6LAAC;oCAAE,WAAU;8CACV,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzB;GA/NwB;;QACJ,yMAAA,CAAA,kBAAe;QAEhB,gLAAA,CAAA,YAAS;;;KAHJ", "debugId": null}}, {"offset": {"line": 1698, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/sections/ScreenshotsSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"framer-motion\";\nimport { useRef } from \"react\";\nimport { useTranslations } from \"next-intl\";\n\nexport default function ScreenshotsSection() {\n  const tScreenshots = useTranslations(\"Screenshots\");\n  const tContentGeneration = useTranslations(\"ContentGeneration\");\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, margin: \"-100px\" });\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.3,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 50 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.8,\n      },\n    },\n  };\n\n  return (\n    <section className=\"py-24 relative\" ref={ref}>\n      <div className=\"max-w-6xl mx-auto px-5\">\n        <div className=\"text-center mb-20\">\n          <h2 className=\"text-4xl lg:text-5xl font-black text-white mb-6 leading-tight\">\n            {tScreenshots(\"sectionTitle\")}\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\">\n            {tScreenshots(\"sectionSubtitle\")}\n          </p>\n        </div>\n\n        <motion.div\n          className=\"space-y-20\"\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate={isInView ? \"visible\" : \"hidden\"}\n        >\n          {/* Screenshot 1: Photo Upload */}\n          <motion.div\n            className=\"flex flex-col lg:flex-row items-center gap-12\"\n            variants={itemVariants}\n          >\n            <div className=\"flex-1 rounded-2xl\">\n              <div className=\"bg-bg-secondary border border-border rounded-xl\">\n                {/* Header with title and filter tabs */}\n                <div className=\"mb-6 bg-bg-primary\">\n                  <div className=\"mb-4 pt-6 pl-6\">\n                    <h4 className=\"text-xl font-bold text-white mb-2\">\n                      Camera Roll Analyzer\n                    </h4>\n                    <span className=\"text-sm text-gray-400\">\n                      AI analyzes photos for trending content\n                    </span>\n                  </div>\n                  <div className=\"flex gap-2 flex-wrap pl-6 pb-4\">\n                    <div className=\"px-3 py-1.5 bg-red-500 text-white text-xs font-medium rounded-lg\">\n                      All (24)\n                    </div>\n                    <div className=\"px-3 py-1.5 bg-bg-secondary text-gray-300 text-xs font-medium rounded-lg  border-1 border-border hover:bg-bg-tertiary cursor-pointer\">\n                      Fashion (8)\n                    </div>\n                    <div className=\"px-3 py-1.5 bg-bg-secondary  text-gray-300 text-xs font-medium border-1 border-border rounded-lg hover:bg-bg-tertiary cursor-pointer\">\n                      Lifestyle (6)\n                    </div>\n                    <div className=\"px-3 py-1.5 bg-bg-secondary text-gray-300 text-xs font-medium border-1 border-border rounded-lg hover:bg-bg-tertiary cursor-pointer\">\n                      Food (4)\n                    </div>\n                    <div className=\"px-3 py-1.5 bg-bg-secondary  text-gray-300 text-xs font-medium border-1 border-border rounded-lg hover:bg-bg-tertiary cursor-pointer\">\n                      Travel (6)\n                    </div>\n                  </div>\n                </div>\n\n                {/* Photo Grid */}\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 p-6 pt-0\">\n                  <div className=\"bg-bg-secondary rounded-lg border-1 border-[#ff6b7a]\">\n                    <div className=\"relative mb-3\">\n                      <div\n                        className=\"w-full h-24 rounded-ss-lg rounded-se-lg text-2xl flex justify-center items-center\"\n                        style={{\n                          background:\n                            \"linear-gradient(135deg, #ff6b7a 0%, #c44569 100%)\",\n                        }}\n                      >\n                        👗\n                      </div>\n                    </div>\n                    <div className=\"space-y-2 p-3\">\n                      <div className=\"text-text-secondary font-medium text-sm\">\n                        autumn_outfit_2024.jpg\n                      </div>\n                      <div className=\"text-text-muted text-xs leading-relaxed line-clamp-2\">\n                        Cozy autumn sweater with matching accessories, perfect\n                        for fall weather trends\n                      </div>\n                      <div className=\"flex justify-between items-center\">\n                        <span className=\"text-text-muted text-xs\">\n                          Oct 28, 2024\n                        </span>\n                        <span\n                          style={{\n                            background: \"rgba(255, 46, 77, 0.15)\",\n                          }}\n                          className=\"px-2 py-1  text-accent-red text-xs rounded-full\"\n                        >\n                          Used\n                        </span>\n                      </div>\n                      <div className=\"flex gap-1 flex-wrap\">\n                        <span className=\"px-2 py-1 bg-bg-tertiary text-text-secondary text-xs rounded-lg\">\n                          autumn\n                        </span>\n                        <span className=\"px-2 py-1 bg-bg-tertiary text-text-secondary text-xs rounded-lg\">\n                          fashion\n                        </span>\n                        <span className=\"px-2 py-1 bg-bg-tertiary text-text-secondary  text-xs rounded-lg\">\n                          cozy\n                        </span>\n                      </div>\n                      <div className=\"inline-block px-2 py-1 bg-accent-red mt-1 text-white text-xs rounded-lg\">\n                        Fashion\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"bg-bg-secondary rounded-lg pb-4 border border-gray-600 hover:border-gray-500 transition-colors\">\n                    <div className=\"relative mb-3\">\n                      <div\n                        className=\"w-full h-24  rounded-ss-lg rounded-se-lg text-2xl flex justify-center items-center\"\n                        style={{\n                          background:\n                            \"linear-gradient(135deg, #4834d4 0%, #686de0 100%)\",\n                        }}\n                      >\n                        ☕\n                      </div>\n                    </div>\n                    <div className=\"space-y-2 p-3\">\n                      <div className=\"text-text-secondary font-medium text-sm\">\n                        morning_coffee.jpg\n                      </div>\n                      <div className=\"text-text-muted text-xs leading-relaxed\">\n                        Perfect morning coffee setup with natural lighting\n                      </div>\n                      <div className=\"flex justify-between items-center\">\n                        <span className=\"text-text-muted text-xs\">\n                          Oct 27, 2024\n                        </span>\n                        <span className=\"px-2 py-1 bg-bg-tertiary text-white text-xs rounded-full\">\n                          Unused\n                        </span>\n                      </div>\n                      <div className=\"flex gap-1 flex-wrap\">\n                        <span className=\"px-2 py-1 bg-bg-tertiary text-gray-200 text-xs rounded-full\">\n                          coffee\n                        </span>\n                        <span className=\"px-2 py-1 bg-bg-tertiary  text-gray-200 text-xs rounded-full\">\n                          morning\n                        </span>\n                      </div>\n                      <div className=\"inline-block px-2 py-1  bg-accent-red mt-1 text-white text-xs rounded-full\">\n                        Lifestyle\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"flex-1 space-y-6\">\n              <h3 className=\"text-2xl font-bold text-white\">\n                Start with Your Photos\n              </h3>\n              <p className=\"text-gray-300 leading-relaxed\">\n                Upload any photo and watch AI instantly match it with trending\n                topics. Your visuals drive the content, not the other way\n                around.\n              </p>\n            </div>\n          </motion.div>\n\n          {/* Screenshot 2: Persona Selection */}\n          <motion.div\n            className=\"screenshot-item reverse\"\n            variants={itemVariants}\n          >\n            <div className=\"screenshot-image\">\n              <div className=\"screenshot-content\">\n                <div className=\"demo-persona-manager-compact !px-6\">\n                  <div className=\"persona-grid\">\n                    <div className=\"persona-card active\">\n                      <div className=\"persona-card-avatar fiona\">F</div>\n                      <h4>Fashion Fiona</h4>\n                      <p>ENFP • Fashion & Lifestyle</p>\n                      <div className=\"persona-knowledge-preview\">\n                        <div className=\"knowledge-chip-mini active\">\n                          Fashion\n                        </div>\n                        <div className=\"knowledge-chip-mini active\">Style</div>\n                        <div className=\"knowledge-chip-mini active\">Trends</div>\n                      </div>\n                    </div>\n\n                    <div className=\"persona-card\">\n                      <div className=\"persona-card-avatar marcus\">M</div>\n                      <h4>Chef Marcus</h4>\n                      <p>ISFJ • Culinary Expert</p>\n                      <div className=\"persona-knowledge-preview\">\n                        <div className=\"knowledge-chip-mini\">Recipes</div>\n                        <div className=\"knowledge-chip-mini\">Cooking</div>\n                        <div className=\"knowledge-chip-mini\">Food</div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"persona-detail-summary\">\n                    <h4\n                      style={{\n                        color: \"var(--text-primary)\",\n                        marginBottom: \"12px\",\n                        fontSize: \"16px\",\n                      }}\n                    >\n                      Selected: Fashion Fiona\n                    </h4>\n                    <p\n                      style={{\n                        color: \"var(--text-secondary)\",\n                        fontSize: \"14px\",\n                        lineHeight: \"1.5\",\n                        marginBottom: \"16px\",\n                      }}\n                    >\n                      A fashion-forward influencer who stays ahead of trends and\n                      creates authentic style content. Reads fashion feeds and\n                      writes with trendy enthusiasm.\n                    </p>\n                    <div className=\"knowledge-sources-summary\">\n                      <div className=\"knowledge-chips\">\n                        <div className=\"knowledge-chip active\">Vogue RSS</div>\n                        <div className=\"knowledge-chip active\">\n                          Fashion Week\n                        </div>\n                        <div className=\"knowledge-chip active\">Style Blogs</div>\n                        <div className=\"knowledge-chip\">Beauty Tips</div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"screenshot-info\">\n              <h3>Personas with Custom Knowledge</h3>\n              <p>\n                Each persona reads different sources and writes in their unique\n                style. Fashion Fiona follows fashion feeds, while Foodie Fred\n                reads culinary blogs.\n              </p>\n            </div>\n          </motion.div>\n\n          {/* Screenshot 3: AI Generation Process */}\n          <motion.div className=\"screenshot-item\" variants={itemVariants}>\n            <div className=\"screenshot-image\">\n              <div className=\"screenshot-content\">\n                <div className=\"demo-content-generation-compact\">\n                  <div className=\"generation-process\">\n                    <div className=\"process-steps\">\n                      <div className=\"step-item active\">\n                        <div className=\"step-icon\">📸</div>\n                        <span>{tContentGeneration(\"steps.photoAnalysis\")}</span>\n                      </div>\n                      <div className=\"step-arrow\">→</div>\n                      <div className=\"step-item active\">\n                        <div className=\"step-icon\">📈</div>\n                        <span>{tContentGeneration(\"steps.trendMatch\")}</span>\n                      </div>\n                      <div className=\"step-arrow\">→</div>\n                      <div className=\"step-item active\">\n                        <div className=\"step-icon\">✨</div>\n                        <span>{tContentGeneration(\"steps.generate\")}</span>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"generated-content-preview bg-bg-secondary\">\n                    <div className=\"content-example\">\n                      <div className=\"content-header\">\n                        <div className=\"persona-badge\">\n                          {tContentGeneration(\"badges.fashionFiona\")}\n                        </div>\n                        <div className=\"trend-badge\">\n                          {tContentGeneration(\"badges.autumnTrends\")}\n                        </div>\n                      </div>\n                      <div className=\"content-body\">\n                        <p>{tContentGeneration(\"sampleContent\")}</p>\n                      </div>\n                      <div className=\"content-platforms\">\n                        <span className=\"platform-icon\">📱</span>\n                        <span className=\"platform-icon\">📷</span>\n                        <span className=\"platform-icon\">🐦</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"screenshot-info\">\n              <h3>{tScreenshots(\"smartContentGeneration.title\")}</h3>\n              <p>{tScreenshots(\"smartContentGeneration.description\")}</p>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,eAAe,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IACrC,MAAM,qBAAqB,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC3C,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAE/D,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,qBACE,6LAAC;QAAQ,WAAU;QAAiB,KAAK;kBACvC,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,aAAa;;;;;;sCAEhB,6LAAC;4BAAE,WAAU;sCACV,aAAa;;;;;;;;;;;;8BAIlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU;oBACV,SAAQ;oBACR,SAAS,WAAW,YAAY;;sCAGhC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;;8CAEV,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAAoC;;;;;;0EAGlD,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAI1C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAmE;;;;;;0EAGlF,6LAAC;gEAAI,WAAU;0EAAuI;;;;;;0EAGtJ,6LAAC;gEAAI,WAAU;0EAAuI;;;;;;0EAGtJ,6LAAC;gEAAI,WAAU;0EAAsI;;;;;;0EAGrJ,6LAAC;gEAAI,WAAU;0EAAuI;;;;;;;;;;;;;;;;;;0DAO1J,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEACC,WAAU;oEACV,OAAO;wEACL,YACE;oEACJ;8EACD;;;;;;;;;;;0EAIH,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAA0C;;;;;;kFAGzD,6LAAC;wEAAI,WAAU;kFAAuD;;;;;;kFAItE,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAA0B;;;;;;0FAG1C,6LAAC;gFACC,OAAO;oFACL,YAAY;gFACd;gFACA,WAAU;0FACX;;;;;;;;;;;;kFAIH,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAAkE;;;;;;0FAGlF,6LAAC;gFAAK,WAAU;0FAAkE;;;;;;0FAGlF,6LAAC;gFAAK,WAAU;0FAAmE;;;;;;;;;;;;kFAIrF,6LAAC;wEAAI,WAAU;kFAA0E;;;;;;;;;;;;;;;;;;kEAM7F,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEACC,WAAU;oEACV,OAAO;wEACL,YACE;oEACJ;8EACD;;;;;;;;;;;0EAIH,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAA0C;;;;;;kFAGzD,6LAAC;wEAAI,WAAU;kFAA0C;;;;;;kFAGzD,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAA0B;;;;;;0FAG1C,6LAAC;gFAAK,WAAU;0FAA2D;;;;;;;;;;;;kFAI7E,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAA8D;;;;;;0FAG9E,6LAAC;gFAAK,WAAU;0FAA+D;;;;;;;;;;;;kFAIjF,6LAAC;wEAAI,WAAU;kFAA6E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQtG,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAG9C,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCASjD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;;8CAEV,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAA4B;;;;;;8EAC3C,6LAAC;8EAAG;;;;;;8EACJ,6LAAC;8EAAE;;;;;;8EACH,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFAA6B;;;;;;sFAG5C,6LAAC;4EAAI,WAAU;sFAA6B;;;;;;sFAC5C,6LAAC;4EAAI,WAAU;sFAA6B;;;;;;;;;;;;;;;;;;sEAIhD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAA6B;;;;;;8EAC5C,6LAAC;8EAAG;;;;;;8EACJ,6LAAC;8EAAE;;;;;;8EACH,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFAAsB;;;;;;sFACrC,6LAAC;4EAAI,WAAU;sFAAsB;;;;;;sFACrC,6LAAC;4EAAI,WAAU;sFAAsB;;;;;;;;;;;;;;;;;;;;;;;;8DAK3C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,OAAO;gEACL,OAAO;gEACP,cAAc;gEACd,UAAU;4DACZ;sEACD;;;;;;sEAGD,6LAAC;4DACC,OAAO;gEACL,OAAO;gEACP,UAAU;gEACV,YAAY;gEACZ,cAAc;4DAChB;sEACD;;;;;;sEAKD,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAwB;;;;;;kFACvC,6LAAC;wEAAI,WAAU;kFAAwB;;;;;;kFAGvC,6LAAC;wEAAI,WAAU;kFAAwB;;;;;;kFACvC,6LAAC;wEAAI,WAAU;kFAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAE;;;;;;;;;;;;;;;;;;sCASP,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,WAAU;4BAAkB,UAAU;;8CAChD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAY;;;;;;kFAC3B,6LAAC;kFAAM,mBAAmB;;;;;;;;;;;;0EAE5B,6LAAC;gEAAI,WAAU;0EAAa;;;;;;0EAC5B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAY;;;;;;kFAC3B,6LAAC;kFAAM,mBAAmB;;;;;;;;;;;;0EAE5B,6LAAC;gEAAI,WAAU;0EAAa;;;;;;0EAC5B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAY;;;;;;kFAC3B,6LAAC;kFAAM,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;8DAKhC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACZ,mBAAmB;;;;;;kFAEtB,6LAAC;wEAAI,WAAU;kFACZ,mBAAmB;;;;;;;;;;;;0EAGxB,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;8EAAG,mBAAmB;;;;;;;;;;;0EAEzB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAI,aAAa;;;;;;sDAClB,6LAAC;sDAAG,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/B;GApUwB;;QACD,yMAAA,CAAA,kBAAe;QACT,yMAAA,CAAA,kBAAe;QAEzB,gLAAA,CAAA,YAAS;;;KAJJ", "debugId": null}}, {"offset": {"line": 2717, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/sections/CTASection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"framer-motion\";\nimport { useRef, useState } from \"react\";\nimport { useTranslations } from \"next-intl\";\n\nexport default function CTASection() {\n  const tCTA = useTranslations(\"CTA\");\n  const tCommon = useTranslations(\"Common\");\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, margin: \"-100px\" });\n  const [email, setEmail] = useState(\"\");\n  const [isSubmitted, setIsSubmitted] = useState(false);\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (email) {\n      // Simulate form submission\n      setIsSubmitted(true);\n      setEmail(\"\");\n    }\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0, y: 50 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.8,\n        ease: \"easeOut\" as const,\n      },\n    },\n  };\n\n  return (\n    <section className=\"py-24 text-center relative bg-bg-secondary\" ref={ref}>\n      <div className=\"max-w-4xl mx-auto px-5\">\n        <motion.div\n          className=\"space-y-8\"\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate={isInView ? \"visible\" : \"hidden\"}\n        >\n          <h2 className=\"text-4xl lg:text-5xl font-black text-white leading-tight\">\n            {tCTA(\"title\", { title: tCTA(\"viralContent\") })\n              .split(tCTA(\"viralContent\"))\n              .map((part, index) =>\n                index === 0 ? (\n                  part\n                ) : (\n                  <span key={index}>\n                    <span className=\"gradient-text\">\n                      {tCTA(\"viralContent\")}\n                    </span>\n                    {part}\n                  </span>\n                )\n              )}\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed\">\n            {tCTA(\"subtitle\")}\n          </p>\n\n          {!isSubmitted ? (\n            <form className=\"mt-12\" onSubmit={handleSubmit}>\n              <div className=\"flex flex-col sm:flex-row gap-4 max-w-lg mx-auto\">\n                <input\n                  type=\"email\"\n                  value={email}\n                  onChange={(e) => setEmail(e.target.value)}\n                  placeholder={tCTA(\"emailPlaceholder\")}\n                  className=\"flex-1 px-6 py-4 bg-bg-primary border border-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500 focus:ring-2 focus:ring-red-500/20 transition-all duration-300\"\n                  required\n                />\n                <button\n                  type=\"submit\"\n                  className=\"bg-gradient-to-r from-red-500 to-purple-600 text-white px-8 py-2 rounded-lg font-semibold text-base hover:transform hover:-translate-y-1 transition-all duration-300 shadow-lg hover:shadow-red-500/30 whitespace-nowrap\"\n                >\n                  {tCommon(\"getEarlyAccess\")}\n                </button>\n              </div>\n              <p className=\"text-gray-400 text-sm mt-6 max-w-md mx-auto\">\n                {tCTA(\"note\")}\n              </p>\n            </form>\n          ) : (\n            <div className=\"bg-green-500/10 border border-green-500/20 rounded-xl p-8 max-w-md mx-auto\">\n              <h3 className=\"text-xl font-bold text-green-400 mb-3\">\n                {tCTA(\"successTitle\")}\n              </h3>\n              <p className=\"text-gray-300\">{tCTA(\"successMessage\")}</p>\n            </div>\n          )}\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,OAAO,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC7B,MAAM,UAAU,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAChC,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAC/D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,OAAO;YACT,2BAA2B;YAC3B,eAAe;YACf,SAAS;QACX;IACF;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,6LAAC;QAAQ,WAAU;QAA6C,KAAK;kBACnE,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;gBACV,SAAQ;gBACR,SAAS,WAAW,YAAY;;kCAEhC,6LAAC;wBAAG,WAAU;kCACX,KAAK,SAAS;4BAAE,OAAO,KAAK;wBAAgB,GAC1C,KAAK,CAAC,KAAK,iBACX,GAAG,CAAC,CAAC,MAAM,QACV,UAAU,IACR,qBAEA,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;kDACb,KAAK;;;;;;oCAEP;;+BAJQ;;;;;;;;;;kCASnB,6LAAC;wBAAE,WAAU;kCACV,KAAK;;;;;;oBAGP,CAAC,4BACA,6LAAC;wBAAK,WAAU;wBAAQ,UAAU;;0CAChC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,aAAa,KAAK;wCAClB,WAAU;wCACV,QAAQ;;;;;;kDAEV,6LAAC;wCACC,MAAK;wCACL,WAAU;kDAET,QAAQ;;;;;;;;;;;;0CAGb,6LAAC;gCAAE,WAAU;0CACV,KAAK;;;;;;;;;;;6CAIV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX,KAAK;;;;;;0CAER,6LAAC;gCAAE,WAAU;0CAAiB,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjD;GA5FwB;;QACT,yMAAA,CAAA,kBAAe;QACZ,yMAAA,CAAA,kBAAe;QAEd,gLAAA,CAAA,YAAS;;;KAJJ", "debugId": null}}, {"offset": {"line": 2918, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/ScrollToCTA.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect } from \"react\";\n\nexport default function ScrollToCTA() {\n  useEffect(() => {\n    const handleScrollToCTA = (e: Event) => {\n      const target = e.target as HTMLElement;\n      \n      // Check if the clicked element is a CTA button (but not in a form)\n      if (\n        target.classList.contains('btn-primary') &&\n        (target.textContent?.includes('Google') || target.textContent?.includes('Early Access')) &&\n        !target.closest('form')\n      ) {\n        e.preventDefault();\n        \n        // Find the CTA section and scroll to it\n        const ctaSection = document.querySelector('.cta');\n        if (ctaSection) {\n          ctaSection.scrollIntoView({ \n            behavior: 'smooth',\n            block: 'start'\n          });\n        }\n      }\n    };\n\n    // Add event listener to document\n    document.addEventListener('click', handleScrollToCTA);\n\n    // Cleanup\n    return () => {\n      document.removeEventListener('click', handleScrollToCTA);\n    };\n  }, []);\n\n  return null; // This component doesn't render anything\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAIe,SAAS;;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;2DAAoB,CAAC;wBAMtB,qBAA0C;oBAL7C,MAAM,SAAS,EAAE,MAAM;oBAEvB,mEAAmE;oBACnE,IACE,OAAO,SAAS,CAAC,QAAQ,CAAC,kBAC1B,CAAC,EAAA,sBAAA,OAAO,WAAW,cAAlB,0CAAA,oBAAoB,QAAQ,CAAC,gBAAa,uBAAA,OAAO,WAAW,cAAlB,2CAAA,qBAAoB,QAAQ,CAAC,gBAAe,KACvF,CAAC,OAAO,OAAO,CAAC,SAChB;wBACA,EAAE,cAAc;wBAEhB,wCAAwC;wBACxC,MAAM,aAAa,SAAS,aAAa,CAAC;wBAC1C,IAAI,YAAY;4BACd,WAAW,cAAc,CAAC;gCACxB,UAAU;gCACV,OAAO;4BACT;wBACF;oBACF;gBACF;;YAEA,iCAAiC;YACjC,SAAS,gBAAgB,CAAC,SAAS;YAEnC,UAAU;YACV;yCAAO;oBACL,SAAS,mBAAmB,CAAC,SAAS;gBACxC;;QACF;gCAAG,EAAE;IAEL,OAAO,MAAM,yCAAyC;AACxD;GAlCwB;KAAA", "debugId": null}}]}